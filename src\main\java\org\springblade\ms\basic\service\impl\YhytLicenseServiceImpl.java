/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.basic.service.impl;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.basic.excel.YhytLicenseExcel;
import org.springblade.ms.basic.mapper.YhytLicenseMapper;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseDailyCutVO;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.common.utils.ApiCenterUtil;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationListener;
import org.springframework.data.geo.Circle;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.GeoOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 零售户信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@AllArgsConstructor
@Slf4j
public class YhytLicenseServiceImpl extends BaseServiceImpl<YhytLicenseMapper, YhytLicenseEntity> implements IYhytLicenseService, ApplicationListener<ApplicationReadyEvent> {

	private static final String CACHE_POINT_KEY = "ms:yhytLicense:point";

	private final RedisTemplate<String, Object> redisTemplate;

	@Override
	public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
		YhytLicenseServiceImpl bean = applicationReadyEvent.getApplicationContext().getBean(YhytLicenseServiceImpl.class);
		bean.init();
	}

	@Override
	public void init() {
		redisTemplate.delete(CACHE_POINT_KEY);
		CacheUtil.clear("ms:yhytLicense");
		List<YhytLicenseEntity> dingMapList = getDingMapList(new YhytLicenseEntity());
//		List<YhytLicenseEntity> dingMapList = this.list();
		if (IterUtil.isNotEmpty(dingMapList)) {
			GeoOperations<String, Object> geoOperations = redisTemplate.opsForGeo();
			dingMapList.forEach(yhytLicense -> {
				if (yhytLicense.getLongitude() != null && yhytLicense.getLatitude() != null) {
					geoOperations.add(CACHE_POINT_KEY, new RedisGeoCommands.GeoLocation<>(yhytLicense.getId() + "", new Point(yhytLicense.getLongitude().doubleValue(), yhytLicense.getLatitude().doubleValue())));
				}

				CacheUtil.put("ms:yhytLicense", "getById:", yhytLicense.getId(), yhytLicense);
			});
		}
	}


	@Override
	public IPage<YhytLicenseVO> selectYhytLicensePage(IPage<YhytLicenseVO> page, YhytLicenseVO yhytLicense) {
		// 地理排序
		if (yhytLicense.getLongitude() != null && yhytLicense.getLatitude() != null) {
			return  page.setRecords(baseMapper.selectYhytLicensePageByDistance(page, yhytLicense));
		}

    	// 2. 默认使用数据库查询（无地理排序）
    	return page.setRecords(baseMapper.selectYhytLicensePage(page, yhytLicense));
	}


	@Override
	public List<YhytLicenseExcel> exportYhytLicense(Wrapper<YhytLicenseEntity> queryWrapper) {
		List<YhytLicenseExcel> yhytLicenseList = baseMapper.exportYhytLicense(queryWrapper);
		//yhytLicenseList.forEach(yhytLicense -> {
		//	yhytLicense.setTypeName(DictCache.getValue(DictEnum.YES_NO, YhytLicense.getType()));
		//});
		return yhytLicenseList;
	}

	@Override
	@Cacheable(cacheNames = "ms:yhytLicense", key = "'getById:' + #id")
	public YhytLicenseEntity getById(Serializable id) {
		/**
		 * 务必在同步数据时调用 CacheUtil.clear("ms:yhytLicense")
		 */
		return super.getById(id);
	}

	@Override
	@Cacheable(cacheNames = "ms:yhytLicense", key = "'getSelectionList:' + #name", unless = "#result.isEmpty()")
	public List<YhytLicenseEntity> getSelectionList(String name) {
		Page<YhytLicenseVO> page = new Page<>();
		page.setSize(50);
		page.setCurrent(1);
		List<YhytLicenseEntity> result = baseMapper.getSelectionList(page, name);
		return CollectionUtils.isEmpty(result) ? Collections.emptyList() : result;
	}

	@Override
	public List<YhytLicenseEntity> getDingMapList(YhytLicenseEntity param) {
		// 如果两个坐标都不为空，就查范围
		if (param.getLongitude() != null && param.getLatitude() != null) {
			List<YhytLicenseEntity> list = new ArrayList<>();

			if(Boolean.FALSE.equals(redisTemplate.hasKey(CACHE_POINT_KEY))){
				//补充地理空间缓存
				this.init();
			}

			GeoOperations<String, Object> geoOperations = redisTemplate.opsForGeo();
			Circle circle = new Circle(new Point(param.getLongitude().doubleValue(), param.getLatitude().doubleValue()), new Distance(1, RedisGeoCommands.DistanceUnit.KILOMETERS));
			RedisGeoCommands.GeoRadiusCommandArgs args = RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs().includeDistance().includeCoordinates().sortAscending();
			GeoResults<RedisGeoCommands.GeoLocation<Object>> result = geoOperations.radius(CACHE_POINT_KEY, circle, args);
            if (result != null) {
				List<String> idList = result.getContent().stream()
						.map(content -> content.getContent().getName())
						.toList();

				if (IterUtil.isNotEmpty(idList)) {
					idList.forEach(id -> {
						YhytLicenseEntity yhytLicenseEntity = CacheUtil.get("ms:yhytLicense", "getById:", id, YhytLicenseEntity.class, false);
						if(yhytLicenseEntity == null){
							//为空补充缓存
							yhytLicenseEntity = getById(Long.parseLong(id));
							if(yhytLicenseEntity != null){
								CacheUtil.put("ms:yhytLicense", "getById:", id, yhytLicenseEntity);
							}
						}

						if (yhytLicenseEntity != null) {
							boolean isAdd = true;
							if (isAdd && StrUtil.isNotBlank(param.getBizFormat())) {
								if (!yhytLicenseEntity.getBizFormat().equals(param.getBizFormat())) {
									isAdd = false;
								}
							}

//							if (isAdd && StrUtil.isNotBlank(param.getCompanyName())) {
//								if (!yhytLicenseEntity.getCompanyName().contains(param.getCompanyName())) {
//									isAdd = false;
//								}
//							}

//							if (isAdd && StrUtil.isNotBlank(param.getLicNo())) {
//								if (!yhytLicenseEntity.getLicNo().contains(param.getLicNo())) {
//									isAdd = false;
//								}
//							}

							if (isAdd) {
								list.add(yhytLicenseEntity);
							}
						}
					});
				}
			}

			return list;
        }

		return baseMapper.getDingMapList(param);
	}

	@Override
	public List<String> getFormatList() {
		List<String> formatList = baseMapper.getFormatList();
		formatList.add(0,"全部");
		formatList.add("无证零售户");
		return formatList;
	}

	@Override
	public Boolean updateLocation(YhytLicenseEntity entity) {
		boolean b = saveOrUpdate(entity);
		updateCacheAfterUpdate(entity);
		return b;
	}

	//更新缓存
	private void updateCacheAfterUpdate(YhytLicenseEntity entity) {
		redisTemplate.delete("ms:yhytLicense::getById:"+entity.getId());
		redisTemplate.delete("ms:dingapp:license:id:" + entity.getId());
		redisTemplate.delete("ms:dingapp:license:licNo:" + entity.getLicNo());

		// 更新Redis地理空间
		GeoOperations<String, Object> geoOps = redisTemplate.opsForGeo();
		geoOps.remove(CACHE_POINT_KEY, entity.getId().toString());
		geoOps.add(
				CACHE_POINT_KEY,
				new RedisGeoCommands.GeoLocation<>(
						entity.getId().toString(),
						new Point(
								entity.getLongitude().doubleValue(),
								entity.getLatitude().doubleValue()
						)
				)
		);
	}


	/**
	 * 从API获取零售许可证信息日切表数据
	 *
	 * @param licNo 许可证号
	 * @return 零售许可证信息日切表VO
	 */
	@Override
	public YhytLicenseDailyCutVO getYhytLicenseDailyCutVO(String licNo) {
		if (Func.isEmpty(licNo)) {
			return null;
		}

		// 构建Redis缓存键
		String cacheKey = "ms:yhytLicense:dailyCut:" + licNo;

		try {
			// 1. 先从Redis缓存中获取数据
			Object cachedData = redisTemplate.opsForValue().get(cacheKey);
			if (cachedData != null) {
				log.debug("从Redis缓存获取零售许可证日切数据，licNo: {}", licNo);
				return (YhytLicenseDailyCutVO) cachedData;
			}

			// 2. 缓存中没有数据，从API获取
			log.debug("缓存中无数据，从API获取零售许可证日切数据，licNo: {}", licNo);

			// 构建请求参数
			Map<String, Object> requestParams = new HashMap<>();
			// 设置FQZD为前一天的yyyymmdd格式
			String yesterdayStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
			requestParams.put("FQZD", yesterdayStr);
			requestParams.put("XKZBH", licNo); // 许可证编号
			requestParams.put("PAGENUM", 1);
			requestParams.put("PAGESIZE", 1);

			// 调用API，serviceId=sv251474XV9R
			String serviceId = "sv251474XV9R";
			String response = ApiCenterUtil.send(serviceId, requestParams);

			if (Func.isNotEmpty(response)) {
				JSONObject jsonObject = JSONUtil.parseObj(response);
				JSONObject dataObj = jsonObject.getJSONObject("data");
				Integer errcode = jsonObject.getInt("errcode");

				if (dataObj != null && errcode == 0) {
					JSONArray dataArray = dataObj.getJSONArray("data");
					if (dataArray != null && !dataArray.isEmpty()) {
						// 取第一条数据
						JSONObject item = dataArray.getJSONObject(0);
						YhytLicenseDailyCutVO vo = new YhytLicenseDailyCutVO();

						try {
							// 基本字符串字段
							vo.setLxr(item.getStr("LXR"));
							vo.setKhbm(item.getStr("KHBM"));
							vo.setJydz(item.getStr("JYDZ"));
							vo.setDsrmc(item.getStr("DSRMC"));
							vo.setQymc(item.getStr("QYMC"));
							vo.setLxdh(item.getStr("LXDH"));
							vo.setXkzbh(item.getStr("XKZBH"));
							vo.setLshuuid(item.getStr("LSHUUID"));
							vo.setXkzzt(item.getStr("XKZZT"));
							vo.setWd(item.getStr("WD"));
							vo.setJd(item.getStr("JD"));
							vo.setXkzyxqxz(item.getStr("XKZYXQXZ"));
							vo.setSxsjxkzzxsj(item.getStr("SXSJXKZZXSJ"));
							vo.setXkzyxqxq(item.getStr("XKZYXQXQ"));
							vo.setSqtyqxz(item.getStr("SQTYQXZ"));
							vo.setSqtyqxq(item.getStr("SQTYQXQ"));
						} catch (Exception e) {
							log.error("转换YhytLicenseDailyCutVO失败", e);
						}

						// 3. 将结果存入Redis缓存，设置1天过期时间
						try {
							redisTemplate.opsForValue().set(cacheKey, vo, 1, TimeUnit.DAYS);
							log.debug("零售许可证日切数据已缓存到Redis，licNo: {}", licNo);
						} catch (Exception e) {
							log.error("缓存零售许可证日切数据到Redis失败，licNo: {}", licNo, e);
						}

						return vo;
					}
				} else {
					log.warn("API返回错误代码: {}, 错误信息: {}",
							dataObj != null ? dataObj.getInt("errcode") : "unknown",
							dataObj != null ? dataObj.getStr("errmsg") : "unknown");
				}
			}
		} catch (Exception e) {
			log.error("从API获取零售许可证信息日切表数据失败，licNo: {}", licNo, e);
		}

		return null;
	}
}
