package org.springblade.ms.dingapp.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseDailyCutVO;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.dingapp.service.IDingLicenseService;
import org.springblade.ms.dingapp.vo.DingMapLicenseVO;
import org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo;
import org.springblade.ms.schoolInfo.service.MsSchoolInfoService;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-23 23:49
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/license")
public class DingLicenseController extends BladeController {

    private final IDingLicenseService dingMapService;
    private final IYhytLicenseService yhytLicenseService;
    private final MsSchoolInfoService schoolInfoService;
    @GetMapping("/getDingMapLicenseList")
    public R<List<DingMapLicenseVO>> getDingMapLicenseList(
            @RequestParam(name = "searchParam", required = false) String searchParam,
            @RequestParam(name = "formatParam", required = false) String formatParam,
            @RequestParam(name = "longitude", required = false) BigDecimal longitude,
            @RequestParam(name = "latitude", required = false) BigDecimal latitude
    ) {
        List<DingMapLicenseVO> list = dingMapService.getDingMapLicenseList(searchParam, formatParam, longitude, latitude);
        return data(list);
    }

    @GetMapping("/getDingMapLicense")
    public R<DingMapLicenseVO> getDingMapLicense(
            @RequestParam(name = "yhytId", required = false) String yhytId,
            @RequestParam(name = "licNo", required = false) String licNo
    ) {
        DingMapLicenseVO vo = dingMapService.getDingMapLicense(yhytId, licNo);
        return data(vo);
    }

    /**
     * 获取业态下拉框
     * @return
     */
    @GetMapping("/getFormatList")
    public R<List<String>> getFormatList() {
        return R.data(yhytLicenseService.getFormatList());
    }

    @GetMapping("/selectYhytPage")
    public R<IPage<DingMapLicenseVO>> selectYhytPage(
            @RequestParam(name = "searchParam", required = false) String searchParam,
            @RequestParam(name = "formatParam", required = false) String formatParam,
            @RequestParam(name = "longitude", required = false) BigDecimal longitude,
            @RequestParam(name = "latitude", required = false) BigDecimal latitude,
            Query query
    ) {
        IPage<DingMapLicenseVO> page = dingMapService.selectYhytPage(searchParam, formatParam, Condition.getPage(query),longitude,latitude);
        return R.data(page);
    }


    //更新零售户周边是否有学校字段  手动
    @GetMapping("/hasSchoolNearby")
    public R<Boolean> hasSchoolNearby(){
        QueryWrapper<MsSchoolInfo> qw = new QueryWrapper<>();
        qw.select("id","latitude","longitude");
        List<MsSchoolInfo> schoolInfoList = schoolInfoService.list(qw);
        QueryWrapper<YhytLicenseEntity> qw1 = new QueryWrapper<>();
        qw1.select("id","latitude","longitude");
        List<YhytLicenseEntity> yhytLicenseEntityList = yhytLicenseService.list();

        for (YhytLicenseEntity yhytLicenseEntity : yhytLicenseEntityList) {
            if(ObjectUtil.isNull(yhytLicenseEntity.getLatitude())||ObjectUtil.isNull(yhytLicenseEntity.getLongitude())){
                continue;
            }
            boolean hasSchoolNearby = false;
            for (MsSchoolInfo school : schoolInfoList) {
                if(ObjectUtil.isNotNull(school.getLatitude())&&ObjectUtil.isNotNull(school.getLongitude())){
                    double distance = CommonUtil.calculateDistance(yhytLicenseEntity.getLatitude().doubleValue(), yhytLicenseEntity.getLongitude().doubleValue(),
                            school.getLatitude().doubleValue(), school.getLongitude().doubleValue());
                    if (distance <= 50) {
                        hasSchoolNearby = true;
                        break;
                    }
                }
            }
            yhytLicenseEntity.setHasSchoolNearby(hasSchoolNearby);
            UpdateWrapper<YhytLicenseEntity> uw = new UpdateWrapper<>();
            uw.eq("id", yhytLicenseEntity.getId())
                    .set("has_school_nearby",yhytLicenseEntity.getHasSchoolNearby());
            yhytLicenseService.update(uw);

        }

        return R.success();
    }

    @GetMapping("/getSelectionList")
    public R<List<YhytLicenseEntity>> getSelectionList(@RequestParam("name") String name) {
        return R.data(yhytLicenseService.getSelectionList(name));
    }

    @GetMapping("/getDetail")
    public R<YhytLicenseEntity> getDetailByLicNo(@RequestParam(name = "licNo", required = false) String licNo) {
        QueryWrapper<YhytLicenseEntity> qw = new QueryWrapper<>();
        qw.eq("lic_no",licNo);
        return R.data(yhytLicenseService.getOne(qw));
    }

    /**
     * 根据许可证号获取许可证信息
     * @param licNo
     * @return
     */
    @GetMapping("/getLicense")
    public R<YhytLicenseDailyCutVO> getLicenseByLicNo(@RequestParam(name = "licNo", required = false) String licNo) {
        return R.data(yhytLicenseService.getYhytLicenseDailyCutVO(licNo));
    }

}
